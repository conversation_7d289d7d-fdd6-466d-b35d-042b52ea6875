---
name: Bug report
about: Create a bug report to improve valet-linux
title: 'Bug: <Your-Title>'
labels: Bug, Needs review
assignees: ''

---

**Basic info**

| Distro (Name and version) | PHP Version | Valet version |
| ------------------------- | ----------- | ------------- |
| Ex: Manjaro 18.3          | 7.3.1       | 2.2.3         |

- [ ] I've checked the issue queue and could not find anything similar to my bug.
- [ ] I'm on the latest version of valet-linux (`valet --version`): `<Valet-Linux-Version>`
- [ ] I've run `valet fix` and `valet install` after updating and before submitting my issue/feature.

**What is the problem?**
A description of what you think the problem is.

**What was supposed to happen?**
A description of what you think was supposed to happen.

**What actually happened?**
A description of what actually happened.

**How to reproduce this?**
A step by step guide on how to reproduce this issue.

**What is the solution?**
A description of the proposed solution.

**Sources**
All sources related to the bug. If the bug uses external tools like PHP extensions it should at
least contain a link to the tool. Any other media which proves helpful can be included here.
