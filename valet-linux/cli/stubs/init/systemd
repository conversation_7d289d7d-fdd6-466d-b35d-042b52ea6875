[Unit]
Description=Valet DNS Resolution
After=network-online.target
Wants=network-online.target systemd-networkd-wait-online.service

[Service]
Restart=on-abnormal

; User and group the process will run as.
User=root
Group=root

Type=forking
ExecStart=/opt/valet-linux/valet-dns start
ExecStop=/opt/valet-linux/valet-dns stop
ExecReload=/opt/valet-linux/valet-dns restart

KillMode=process

; Use a minimal /dev
PrivateDevices=true
; Sadly home needs to be unprotected for PHP 7.4 (Any updates will fix this)
ProtectHome=false
CapabilityBoundingSet=

[Install]
WantedBy=multi-user.target