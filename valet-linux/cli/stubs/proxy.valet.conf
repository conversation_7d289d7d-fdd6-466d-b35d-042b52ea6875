# valet stub: proxy.valet.conf

server {
    listen 80;
    server_name VALET_SITE www.VALET_SITE *.VALET_SITE;
    root /;
    charset utf-8;
    client_max_body_size 128M;

    location /VALET_STATIC_PREFIX/ {
        internal;
        alias /;
        try_files $uri $uri/;
    }

    access_log off;
    error_log "VALET_HOME_PATH/Log/VALET_SITE-error.log";

    error_page 404 "VALET_SERVER_PATH";

    location / {
        proxy_pass VALET_PROXY_HOST;
        proxy_set_header   Host              $host;
        proxy_set_header   X-Real-IP         $remote_addr;
        proxy_set_header   X-Forwarded-For   $proxy_add_x_forwarded_for;
        proxy_set_header   X-Forwarded-Proto $scheme;
        proxy_http_version 1.1;
        proxy_intercept_errors on;
        proxy_request_buffering off;
        proxy_buffering off;
    }

    location ~ /\.ht {
        deny all;
    }
}

server {
    listen 60;
    server_name VALET_SITE www.VALET_SITE *.VALET_SITE;
    root /;
    charset utf-8;
    client_max_body_size 128M;

    add_header X-Robots-Tag 'noindex, nofollow, nosnippet, noarchive';

    location /VALET_STATIC_PREFIX/ {
        internal;
        alias /;
        try_files $uri $uri/;
    }

    access_log off;
    error_log "VALET_HOME_PATH/Log/VALET_SITE-error.log";

    error_page 404 "VALET_SERVER_PATH";

    location / {
        proxy_pass VALET_PROXY_HOST;
        proxy_set_header   Host              $host;
        proxy_set_header   X-Real-IP         $remote_addr;
        proxy_set_header   X-Forwarded-For   $proxy_add_x_forwarded_for;
        proxy_set_header   X-Forwarded-Proto $scheme;
    }

    location ~ /\.ht {
        deny all;
    }
}

