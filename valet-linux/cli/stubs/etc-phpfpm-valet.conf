; FPM pool configuration for Vale<PERSON>

[valet]
user = VALET_USER
group = staff
listen = VALET_HOME_PATH/valet.sock
listen.owner = VALET_USER
listen.group = staff
listen.mode = 0777

;; When uncommented, the following values will take precedence over settings declared elsewhere
;php_admin_value[memory_limit] = 512M
;php_admin_value[upload_max_filesize] = 128M
;php_admin_value[post_max_size] = 128M

;php_admin_value[error_log] = VALET_HOME_PATH/Log/php-fpm.log
;php_admin_flag[log_errors] = on


;; Note: increasing these values will increase the demand on your CPU and RAM resources
pm = dynamic
pm.max_children = 5
pm.start_servers = 2
pm.min_spare_servers = 1
pm.max_spare_servers = 3
