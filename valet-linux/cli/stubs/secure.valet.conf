server {
    listen VALET_HTTP_PORT;
    server_name VALET_SITE www.VALET_SITE *.VALET_SITE;
    return 301 https://$hostVALET_REDIRECT_PORT$request_uri;
}

server {
    listen VALET_HTTPS_PORT ssl http2;
    server_name VALET_SITE www.VALET_SITE *.VALET_SITE;
    root /;
    charset utf-8;

    location /VALET_STATIC_PREFIX/ {
        internal;
        alias /;
        try_files $uri $uri/;
    }

    ssl_certificate VALET_CERT;
    ssl_certificate_key VALET_KEY;

    location / {
        rewrite ^ VALET_SERVER_PATH last;
    }

    location = /favicon.ico { access_log off; log_not_found off; }
    location = /robots.txt  { access_log off; log_not_found off; }

    access_log off;
    error_log VALET_HOME_PATH/Log/nginx-error.log;

    error_page 404 VALET_SERVER_PATH;

    location ~ \.php$ {
        fastcgi_split_path_info ^(.+\.php)(/.+)$;
        fastcgi_pass unix:VALET_HOME_PATH/valet.sock;
        fastcgi_index VALET_SERVER_PATH;
        include fastcgi_params;
        fastcgi_param SCRIPT_FILENAME VALET_SERVER_PATH;
    }

    location ~ /\.ht {
        deny all;
    }
}

server {
    listen 88;
    server_name VALET_SITE www.VALET_SITE *.VALET_SITE;
    root /;
    charset utf-8;
    client_max_body_size 128M;

    location /VALET_STATIC_PREFIX/ {
        internal;
        alias /;
        try_files $uri $uri/;
    }

    location / {
        rewrite ^ VALET_SERVER_PATH last;
    }

    access_log off;
    error_log VALET_HOME_PATH/Log/nginx-error.log;

    error_page 404 VALET_SERVER_PATH;

    location ~ \.php$ {
        fastcgi_split_path_info ^(.+\.php)(/.+)$;
        fastcgi_pass unix:VALET_HOME_PATH/valet.sock;
        fastcgi_index VALET_SERVER_PATH;
        include fastcgi_params;
        fastcgi_param SCRIPT_FILENAME VALET_SERVER_PATH;
    }

    location ~ /\.ht {
        deny all;
    }
}

