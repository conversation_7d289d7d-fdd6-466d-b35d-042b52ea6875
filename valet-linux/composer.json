{"name": "cpriego/valet-linux", "description": "A more enjoyable local development experience for Linux.", "keywords": ["laravel", "zonda", "wwdhhd", "ubuntu", "fedora", "arch", "linux", "valet"], "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "autoload": {"files": ["cli/includes/compatibility.php", "cli/includes/facades.php", "cli/includes/helpers.php"], "psr-4": {"Valet\\": "cli/Valet/"}}, "autoload-dev": {"psr-4": {"Valet\\Tests\\": "tests/"}}, "require": {"php": ">=7.0", "illuminate/container": "~5.3|^6.0|^7.0|^8.0|^9.0|^10.0|^11.00|^12.00", "mnapoli/silly": "~1.1", "symfony/process": "~2.7|~3.0|~4.0|~5.0|^6.0|^7.0", "nategood/httpful": "~1.0", "tightenco/collect": "~5.3|^6.0|^7.0|^8.0|^9.0", "ext-posix": "*", "ext-json": "*", "outrightvision/api-model": "^1.0"}, "require-dev": {"mockery/mockery": "^1.2.3", "phpunit/phpunit": "~5.5|^9.0"}, "scripts": {"post-install-cmd": [], "post-update-cmd": []}, "bin": ["valet"]}